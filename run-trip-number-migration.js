/**
 * Run Global Unique Trip Numbers Migration
 * This script applies the migration to implement globally unique trip numbers
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'hauling_qr_system',
  password: process.env.DB_PASSWORD || 'admin',
  port: process.env.DB_PORT || 5432,
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Starting Global Unique Trip Numbers Migration...\n');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, 'database', 'migrations', '020_global_unique_trip_numbers.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Migration file loaded successfully');
    console.log('🔍 Checking current trip_logs state...\n');
    
    // Check current state
    const currentState = await client.query(`
      SELECT 
        COUNT(*) as total_trips,
        COUNT(DISTINCT trip_number) as unique_trip_numbers,
        COUNT(*) - COUNT(DISTINCT trip_number) as duplicates
      FROM trip_logs
    `);
    
    const { total_trips, unique_trip_numbers, duplicates } = currentState.rows[0];
    
    console.log(`📊 Current State:`);
    console.log(`   Total trips: ${total_trips}`);
    console.log(`   Unique trip numbers: ${unique_trip_numbers}`);
    console.log(`   Duplicate trip numbers: ${duplicates}`);
    
    if (duplicates > 0) {
      console.log(`\n⚠️  Found ${duplicates} duplicate trip numbers - migration needed`);
    } else {
      console.log(`\n✅ No duplicates found, but will still apply global uniqueness constraint`);
    }
    
    // Show some examples of current trip numbers
    console.log('\n📋 Sample current trip numbers:');
    const samples = await client.query(`
      SELECT id, assignment_id, trip_number, status, created_at
      FROM trip_logs
      ORDER BY id DESC
      LIMIT 5
    `);
    
    samples.rows.forEach(row => {
      console.log(`   ID: ${row.id}, Assignment: ${row.assignment_id}, Trip#: ${row.trip_number}, Status: ${row.status}`);
    });
    
    console.log('\n🔄 Applying migration...\n');
    
    // Apply the migration
    await client.query(migrationSQL);
    
    console.log('✅ Migration applied successfully!\n');
    
    // Check final state
    const finalState = await client.query(`
      SELECT 
        COUNT(*) as total_trips,
        COUNT(DISTINCT trip_number) as unique_trip_numbers,
        COUNT(*) - COUNT(DISTINCT trip_number) as duplicates
      FROM trip_logs
    `);
    
    const finalStats = finalState.rows[0];
    
    console.log(`📊 Final State:`);
    console.log(`   Total trips: ${finalStats.total_trips}`);
    console.log(`   Unique trip numbers: ${finalStats.unique_trip_numbers}`);
    console.log(`   Duplicate trip numbers: ${finalStats.duplicates}`);
    
    // Show updated trip numbers
    console.log('\n📋 Sample updated trip numbers:');
    const updatedSamples = await client.query(`
      SELECT id, assignment_id, trip_number, status, created_at
      FROM trip_logs
      ORDER BY id DESC
      LIMIT 5
    `);
    
    updatedSamples.rows.forEach(row => {
      console.log(`   ID: ${row.id}, Assignment: ${row.assignment_id}, Trip#: ${row.trip_number}, Status: ${row.status}`);
    });
    
    // Verify constraint exists
    const constraintCheck = await client.query(`
      SELECT constraint_name, constraint_type
      FROM information_schema.table_constraints
      WHERE table_name = 'trip_logs' 
        AND constraint_name = 'unique_global_trip_number'
    `);
    
    if (constraintCheck.rows.length > 0) {
      console.log('\n✅ Global unique constraint successfully created');
    } else {
      console.log('\n❌ Warning: Global unique constraint not found');
    }
    
    console.log('\n🎯 Migration Summary:');
    console.log('✅ Trip numbers are now globally unique');
    console.log('✅ Each trip number equals its database ID');
    console.log('✅ No more duplicate trip numbers possible');
    console.log('✅ Frontend will display these unique numbers');
    
    console.log('\n🔄 Next Steps:');
    console.log('1. Test the scanner to ensure trip creation works');
    console.log('2. Check Trip Monitoring page for unique trip numbers');
    console.log('3. Verify no duplicate Trip# values in the interface');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    throw error;
  } finally {
    client.release();
  }
}

// Handle command line execution
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log('\n🏁 Migration completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = { runMigration };
