#!/usr/bin/env node

/**
 * Auto-Assignment Creator
 * 
 * This module handles automatic assignment creation when trucks scan at unassigned locations.
 * It creates assignments with 'assigned' status that can be used immediately, eliminating
 * the need for manual approval while maintaining operational continuity.
 */

const { getClient } = require('../config/database');
const { notifyRouteDiscoveryStarted, notifyRouteLocationConfirmed, notifyRouteUpdated, notifyRouteDiscoveryCompleted } = require('../websocket');

class AutoAssignmentCreator {
  constructor() {
    this.logger = console; // Use console for now, can be replaced with proper logger
  }

  /**
   * Create automatic assignment for truck at unassigned location
   * @param {Object} params - Creation parameters
   * @param {Object} params.truck - Truck object with id, truck_number, etc.
   * @param {Object} params.location - Location object with id, name, type, etc.
   * @param {Object} params.client - Database client
   * @param {number} params.userId - User ID who triggered the scan
   * @param {Object} params.preserveOriginalRoute - Optional: Preserve specific loading/unloading locations from exception context
   * @param {number} params.preserveOriginalRoute.loadingLocationId - Specific loading location ID to preserve
   * @param {string} params.preserveOriginalRoute.loadingLocationName - Specific loading location name to preserve
   * @returns {Promise<Object>} Created assignment object
   */
  async createAutoAssignment(params) {
    const { truck, location, client, userId, preserveOriginalRoute, enableDynamicRouting = true, independentAssignment = false } = params;

    this.logger.log(`🎯 Creating auto-assignment for truck ${truck.truck_number} at ${location.name} (Dynamic: ${enableDynamicRouting})`);

    // Use dynamic routing if enabled
    if (enableDynamicRouting) {
      return await this.createDynamicAssignment(params);
    }
    
    // Step 1: Get truck's most recent assignment for context
    const recentAssignmentResult = await client.query(`
      SELECT 
        a.*,
        ll.name as loading_location_name,
        ul.name as unloading_location_name,
        d.id as driver_id,
        d.full_name as driver_name
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND a.status IN ('assigned', 'in_progress', 'completed')
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [truck.truck_number]);
    
    if (recentAssignmentResult.rows.length === 0) {
      throw new Error(`No historical assignments found for truck ${truck.truck_number}. Cannot create auto-assignment without context.`);
    }
    
    const recentAssignment = recentAssignmentResult.rows[0];
    this.logger.log(`📋 Using recent assignment ${recentAssignment.id} as template`);
    
    // Step 2: Determine assignment route based on location type and context
    let loadingLocationId, unloadingLocationId;

    if (preserveOriginalRoute && preserveOriginalRoute.loadingLocationId && location.type === 'unloading') {
      // Exception approval scenario: Preserve original loading location for unloading deviations
      loadingLocationId = preserveOriginalRoute.loadingLocationId;
      unloadingLocationId = location.id;
      this.logger.log(`🚛 Creating assignment (exception approval): ${preserveOriginalRoute.loadingLocationName} (PRESERVED) → ${location.name} (NEW)`);
    } else if (location.type === 'loading') {
      // New loading location - keep recent unloading location
      loadingLocationId = location.id;
      unloadingLocationId = recentAssignment.unloading_location_id;
      this.logger.log(`🚛 Creating assignment: ${location.name} (NEW) → ${recentAssignment.unloading_location_name} (EXISTING)`);
    } else if (location.type === 'unloading') {
      // New unloading location - keep recent loading location (normal QR scan scenario)
      loadingLocationId = recentAssignment.loading_location_id;
      unloadingLocationId = location.id;
      this.logger.log(`🚛 Creating assignment: ${recentAssignment.loading_location_name} (EXISTING) → ${location.name} (NEW)`);
    } else {
      // Unknown location type - default to loading
      loadingLocationId = location.id;
      unloadingLocationId = recentAssignment.unloading_location_id;
      this.logger.log(`🚛 Creating assignment: ${location.name} (NEW, defaulted to loading) → ${recentAssignment.unloading_location_name} (EXISTING)`);
    }
    
    // Step 3: Check if assignment already exists to prevent duplicates (only if not independent assignment)
    if (!options.independentAssignment) {
      const existingAssignmentResult = await client.query(`
        SELECT id, assignment_code, status
        FROM assignments
        WHERE truck_id = $1
          AND loading_location_id = $2
          AND unloading_location_id = $3
          AND status IN ('assigned', 'in_progress')
          AND NOT EXISTS (
            SELECT 1 FROM trip_logs tl
            WHERE tl.assignment_id = assignments.id
              AND tl.status NOT IN ('trip_completed', 'cancelled', 'breakdown')
          )
      `, [truck.id, loadingLocationId, unloadingLocationId]);

      if (existingAssignmentResult.rows.length > 0) {
        const existingAssignment = existingAssignmentResult.rows[0];
        this.logger.log(`📋 Found existing assignment: ${existingAssignment.assignment_code} for same route - reusing instead of creating duplicate`);

        // Return existing assignment
        const fullAssignmentResult = await client.query(`
          SELECT
            a.*,
            ll.name as loading_location_name,
            ul.name as unloading_location_name,
            d.full_name as driver_name
          FROM assignments a
          LEFT JOIN locations ll ON a.loading_location_id = ll.id
          LEFT JOIN locations ul ON a.unloading_location_id = ul.id
          LEFT JOIN drivers d ON a.driver_id = d.id
          WHERE a.id = $1
        `, [existingAssignment.id]);

        return fullAssignmentResult.rows[0];
      }
    } else {
      this.logger.log(`🆕 Creating independent assignment - skipping existing assignment check`);
    }
    
    // Step 4: Generate unique assignment code
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();
    const assignmentCode = `ASG-${timestamp}-${randomSuffix}`;
    
    // Step 5: Create new assignment with 'assigned' status (ready for immediate use)
    const newAssignmentResult = await client.query(`
      INSERT INTO assignments (
        assignment_code,
        truck_id,
        driver_id,
        loading_location_id,
        unloading_location_id,
        assigned_date,
        status,
        priority,
        expected_loads_per_day,
        notes,
        created_at,
        updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
      ) RETURNING *
    `, [
      assignmentCode,
      truck.id,
      recentAssignment.driver_id,
      loadingLocationId,
      unloadingLocationId,
      new Date().toISOString().split('T')[0], // Today's date
      'assigned', // CRITICAL: Set to 'assigned' for immediate use
      recentAssignment.priority || 'medium',
      recentAssignment.expected_loads_per_day || 1,
      JSON.stringify({
        creation_method: 'auto_assignment',
        created_by_user_id: userId,
        trigger_location: {
          id: location.id,
          name: location.name,
          type: location.type
        },
        based_on_assignment: {
          id: recentAssignment.id,
          assignment_code: recentAssignment.assignment_code
        },
        auto_created: true,
        requires_review: true // Flag for admin review but doesn't block usage
      }),
      new Date(),
      new Date()
    ]);
    
    const newAssignment = newAssignmentResult.rows[0];
    
    // Step 6: Get full assignment details with location names
    const fullAssignmentResult = await client.query(`
      SELECT 
        a.*,
        ll.name as loading_location_name,
        ul.name as unloading_location_name,
        d.full_name as driver_name
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE a.id = $1
    `, [newAssignment.id]);
    
    const fullAssignment = fullAssignmentResult.rows[0];
    
    // Step 7: Log the auto-assignment creation for audit purposes (simplified)
    this.logger.log(`📝 Audit log: Auto-assignment ${assignmentCode} created for truck ${truck.truck_number} at ${location.name}`);
    
    this.logger.log(`✅ Auto-assignment created successfully: ${assignmentCode}`);
    this.logger.log(`   Route: ${fullAssignment.loading_location_name} → ${fullAssignment.unloading_location_name}`);
    this.logger.log(`   Status: ${fullAssignment.status} (ready for immediate use)`);
    
    return fullAssignment;
  }

  /**
   * Check if auto-assignment creation is appropriate for this scenario
   * ENHANCED: More intelligent checking to prevent unnecessary dynamic assignments
   * @param {Object} params - Check parameters
   * @param {Object} params.truck - Truck object
   * @param {Object} params.location - Location object
   * @param {Object} params.client - Database client
   * @returns {Promise<Object>} Check result with recommendation
   */
  async shouldCreateAutoAssignment(params) {
    const { truck, location, client } = params;

    // CRITICAL VALIDATION: Check location type before proceeding
    if (location.type !== 'loading' && location.type !== 'unloading') {
      return {
        shouldCreate: false,
        reason: `Invalid location type "${location.type}" for auto-assignment creation`,
        recommendation: 'Only loading or unloading locations are supported for auto-assignment creation'
      };
    }

    // SIMPLIFIED Check 1: Look for reusable assignments with 'assigned' status only
    const reusableAssignmentsResult = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.updated_at,
        a.loading_location_id, a.unloading_location_id,
        ll.name as loading_location, ul.name as unloading_location
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND a.status = 'assigned'
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [truck.id, location.id]);

    if (reusableAssignmentsResult.rows.length > 0) {
      const reusableAssignment = reusableAssignmentsResult.rows[0];
      return {
        shouldCreate: false,
        reason: `Reusable assignment found: ${reusableAssignment.assignment_code} (${reusableAssignment.status})`,
        recommendation: 'Use existing assignment instead of creating new one',
        existingAssignment: reusableAssignment
      };
    }

    // Check 2: Ensure truck has historical assignments for pattern learning
    const historicalAssignmentsResult = await client.query(`
      SELECT COUNT(*) as assignment_count
      FROM assignments
      WHERE truck_id = $1
        AND status IN ('assigned', 'in_progress', 'completed')
    `, [truck.id]);

    const assignmentCount = parseInt(historicalAssignmentsResult.rows[0].assignment_count);

    if (assignmentCount === 0) {
      return {
        shouldCreate: false,
        reason: 'No historical assignments found for truck',
        recommendation: 'Manual assignment creation required'
      };
    }

    // Check 3: Ensure location is valid for assignments
    if (!location.type || !['loading', 'unloading'].includes(location.type)) {
      return {
        shouldCreate: false,
        reason: 'Location type not suitable for assignments',
        recommendation: 'Only loading and unloading locations support auto-assignment'
      };
    }

    // Check 4: Ensure truck is active
    if (truck.status !== 'active') {
      return {
        shouldCreate: false,
        reason: 'Truck is not active',
        recommendation: 'Only active trucks can receive auto-assignments'
      };
    }

    // ENHANCED Check 5: Verify this isn't a duplicate assignment scenario
    const duplicateCheckResult = await client.query(`
      SELECT COUNT(*) as duplicate_count
      FROM assignments a
      WHERE a.truck_id = $1
        AND a.loading_location_id = $2
        AND a.unloading_location_id = $3
        AND a.status = 'assigned'
    `, [truck.id, location.id, location.id]);

    const duplicateCount = parseInt(duplicateCheckResult.rows[0].duplicate_count);
    if (duplicateCount > 0) {
      return {
        shouldCreate: false,
        reason: 'Potential duplicate assignment detected',
        recommendation: 'Check for existing assignments with same truck and location'
      };
    }

    return {
      shouldCreate: true,
      reason: 'All conditions met for auto-assignment creation - no reusable assignments found',
      recommendation: 'Proceed with auto-assignment creation'
    };
  }

  /**
   * Create dynamic assignment with progressive route discovery
   * Only sets the known location, leaves the other location as null for dynamic discovery
   * @param {Object} params - Assignment parameters
   * @returns {Promise<Object>} Created assignment object
   */
  async createDynamicAssignment(params) {
    const { truck, location, client, userId, independentAssignment = false } = params;

    this.logger.log(`🔄 Creating dynamic assignment for truck ${truck.truck_number} at ${location.name}`);

    // CRITICAL VALIDATION: Ensure location type is valid for assignment creation
    if (location.type !== 'loading' && location.type !== 'unloading') {
      const errorMessage = `Invalid location type "${location.type}" for auto-assignment creation at "${location.name}". Only loading or unloading locations are supported.`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    this.logger.log(`✅ Location type validation passed: ${location.type} location at ${location.name}`);

    // Step 1: Get truck's most recent assignment for driver context
    const recentAssignmentResult = await client.query(`
      SELECT
        a.*,
        ll.name as loading_location_name,
        ul.name as unloading_location_name,
        d.full_name as driver_name
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE a.truck_id = $1
        AND a.status IN ('assigned', 'in_progress', 'completed')
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [truck.id]);

    if (recentAssignmentResult.rows.length === 0) {
      throw new Error(`No historical assignments found for truck ${truck.truck_number}. Manual assignment creation required.`);
    }

    const recentAssignment = recentAssignmentResult.rows[0];

    // CRITICAL FIX: Validate recent assignment has valid location IDs
    if (!recentAssignment.loading_location_id || !recentAssignment.unloading_location_id) {
      this.logger.error(`❌ Recent assignment has invalid location IDs`, {
        assignment_id: recentAssignment.id,
        assignment_code: recentAssignment.assignment_code,
        loading_location_id: recentAssignment.loading_location_id,
        unloading_location_id: recentAssignment.unloading_location_id,
        truck_id: truck.id,
        truck_number: truck.truck_number
      });
      throw new Error(`Recent assignment ${recentAssignment.assignment_code} has invalid location IDs. Cannot create dynamic assignment.`);
    }

    // Step 2: Create dynamic assignment based on current location (working with NOT NULL constraints)
    let loadingLocationId;
    let unloadingLocationId;
    let assignmentDescription = '';
    let discoveryMode = '';

    if (location.type === 'loading') {
      // Truck is at loading location - set loading location
      loadingLocationId = location.id;

      if (independentAssignment) {
        // For independent assignments, use recent assignment's unloading as placeholder
        // CRITICAL FIX: Cannot use NULL due to database NOT NULL constraint
        unloadingLocationId = recentAssignment.unloading_location_id;
        assignmentDescription = `${location.name} → [${recentAssignment.unloading_location_name} - placeholder for discovery]`;
        discoveryMode = 'independent_unloading_discovery';
        this.logger.log(`📍 Independent assignment: Loading location confirmed as ${location.name}, unloading location using placeholder ${recentAssignment.unloading_location_name} (will be updated during trip)`);
      } else {
        // Use recent assignment's unloading as placeholder
        unloadingLocationId = recentAssignment.unloading_location_id;
        assignmentDescription = `${location.name} → [${recentAssignment.unloading_location_name} - predicted, will update if different]`;
        discoveryMode = 'unloading_discovery';
        this.logger.log(`📍 Dynamic assignment: Loading location confirmed as ${location.name}, unloading location predicted as ${recentAssignment.unloading_location_name}`);
      }
    } else if (location.type === 'unloading') {
      // Truck is at unloading location - set unloading, use recent assignment's loading as placeholder
      loadingLocationId = recentAssignment.loading_location_id; // Placeholder, will be updated if truck goes elsewhere
      unloadingLocationId = location.id;
      assignmentDescription = `[${recentAssignment.loading_location_name} - predicted, will update if different] → ${location.name}`;
      discoveryMode = 'loading_discovery';
      this.logger.log(`📍 Dynamic assignment: Unloading location confirmed as ${location.name}, loading location predicted as ${recentAssignment.loading_location_name}`);
    } else {
      // Unknown location type - create flexible assignment using recent assignment as template
      loadingLocationId = location.id; // Assume loading, can be updated
      unloadingLocationId = recentAssignment.unloading_location_id; // Use recent as placeholder
      assignmentDescription = `${location.name} (flexible) → [${recentAssignment.unloading_location_name} - predicted]`;
      discoveryMode = 'flexible_discovery';
      this.logger.log(`📍 Dynamic assignment: Flexible assignment starting at ${location.name}, using recent assignment as template`);
    }

    // CRITICAL FIX: Ensure both location IDs are valid (database NOT NULL constraint)
    if (!loadingLocationId || !unloadingLocationId) {
      this.logger.error(`❌ CRITICAL ERROR: Invalid location IDs detected`, {
        loadingLocationId,
        unloadingLocationId,
        location_type: location.type,
        location_id: location.id,
        location_name: location.name,
        recent_assignment_id: recentAssignment.id,
        recent_loading_id: recentAssignment.loading_location_id,
        recent_unloading_id: recentAssignment.unloading_location_id,
        independentAssignment,
        enableDynamicRouting
      });
      throw new Error(`Invalid location IDs: loading=${loadingLocationId}, unloading=${unloadingLocationId}. Both must be valid due to database constraints.`);
    }

    // Step 3: Generate unique assignment code
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();
    const assignmentCode = `DYN-${timestamp}-${randomSuffix}`;

    // Step 4: Check for existing assignment to avoid duplicates (only if not independent assignment)
    if (!independentAssignment) {
      const existingAssignmentResult = await client.query(`
        SELECT id, assignment_code, status FROM assignments
        WHERE truck_id = $1
          AND loading_location_id = $2
          AND unloading_location_id = $3
          AND status IN ('assigned', 'in_progress')
          AND NOT EXISTS (
            SELECT 1 FROM trip_logs tl
            WHERE tl.assignment_id = assignments.id
              AND tl.status NOT IN ('trip_completed', 'cancelled', 'breakdown')
          )
        LIMIT 1
      `, [truck.id, loadingLocationId, unloadingLocationId]);

      if (existingAssignmentResult.rows.length > 0) {
        const existingAssignment = existingAssignmentResult.rows[0];
        this.logger.log(`📋 Found existing assignment ${existingAssignment.assignment_code} for same route - reusing instead of creating duplicate`);

        // Return the existing assignment
        const fullAssignmentResult = await client.query(`
          SELECT
            a.*,
            ll.name as loading_location_name,
            ul.name as unloading_location_name,
            d.full_name as driver_name
          FROM assignments a
          LEFT JOIN locations ll ON a.loading_location_id = ll.id
          LEFT JOIN locations ul ON a.unloading_location_id = ul.id
          LEFT JOIN drivers d ON a.driver_id = d.id
          WHERE a.id = $1
        `, [existingAssignment.id]);

        return {
          success: true,
          assignment: fullAssignmentResult.rows[0],
          message: `Reusing existing assignment ${existingAssignment.assignment_code}`,
        reused: true
      };
      }
    } else {
      this.logger.log(`🆕 Creating independent dynamic assignment - skipping existing assignment check`);
    }

    // Step 5: Create new dynamic assignment with partial route information
    const newAssignmentResult = await client.query(`
      INSERT INTO assignments (
        assignment_code,
        truck_id,
        driver_id,
        loading_location_id,
        unloading_location_id,
        assigned_date,
        status,
        priority,
        expected_loads_per_day,
        notes,
        created_at,
        updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
      ) RETURNING *
    `, [
      assignmentCode,
      truck.id,
      recentAssignment.driver_id,
      loadingLocationId,
      unloadingLocationId,
      new Date().toISOString().split('T')[0], // Today's date
      'assigned', // Set to 'assigned' for immediate use
      recentAssignment.priority || 'medium',
      recentAssignment.expected_loads_per_day || 1,
      JSON.stringify({
        creation_method: 'dynamic_assignment',
        created_by_user_id: userId,
        trigger_location: {
          id: location.id,
          name: location.name,
          type: location.type
        },
        based_on_assignment: {
          id: recentAssignment.id,
          assignment_code: recentAssignment.assignment_code
        },
        route_discovery: {
          mode: 'progressive',
          discovery_type: discoveryMode,
          confirmed_location: {
            id: location.id,
            name: location.name,
            type: location.type,
            role: location.type === 'loading' ? 'loading' : location.type === 'unloading' ? 'unloading' : 'flexible'
          },
          predicted_location: {
            loading_id: loadingLocationId,
            unloading_id: unloadingLocationId,
            based_on_assignment: recentAssignment.assignment_code
          },
          description: assignmentDescription,
          needs_confirmation: true
        },
        auto_created: true,
        requires_review: false // Dynamic assignments don't need review
      }),
      new Date(),
      new Date()
    ]);

    const newAssignment = newAssignmentResult.rows[0];

    // Step 6: Get full assignment details for return
    const fullAssignmentResult = await client.query(`
      SELECT
        a.*,
        ll.name as loading_location_name,
        ul.name as unloading_location_name,
        d.full_name as driver_name
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE a.id = $1
    `, [newAssignment.id]);

    const fullAssignment = fullAssignmentResult.rows[0];

    this.logger.log(`✅ Dynamic assignment created: ${assignmentCode}`);
    this.logger.log(`📋 Route: ${assignmentDescription}`);
    this.logger.log(`🚛 Driver: ${fullAssignment.driver_name}`);

    // Send WebSocket notification for route discovery started
    try {
      notifyRouteDiscoveryStarted({
        id: fullAssignment.id,
        assignment_code: assignmentCode,
        truck_number: truck.truck_number
      }, location);
    } catch (error) {
      this.logger.log(`⚠️  Failed to send route discovery notification: ${error.message}`);
    }

    return fullAssignment;
  }

  /**
   * Update dynamic assignment when truck visits new location
   * @param {Object} params - Update parameters
   * @returns {Promise<Object>} Updated assignment object
   */
  async updateDynamicAssignment(params) {
    const { assignment, location, client } = params;

    this.logger.log(`🔄 Updating dynamic assignment ${assignment.assignment_code} with location ${location.name}`);

    // Parse assignment notes to understand discovery state
    const assignmentNotes = assignment.notes ? JSON.parse(assignment.notes) : {};
    const routeDiscovery = assignmentNotes.route_discovery || {};
    const confirmedLocation = routeDiscovery.confirmed_location || {};

    let updateQuery = '';
    let updateParams = [];
    let description = '';
    let needsUpdate = false;

    // Determine if this location visit confirms or changes the predicted route
    if (location.type === 'loading' && confirmedLocation.type !== 'loading') {
      // Truck visited loading location - update if different from predicted
      if (assignment.loading_location_id !== location.id) {
        updateQuery = `
          UPDATE assignments
          SET loading_location_id = $1, updated_at = $2,
              notes = COALESCE(notes::jsonb, '{}'::jsonb) || $3::jsonb
          WHERE id = $4
          RETURNING *
        `;
        updateParams = [
          location.id,
          new Date(),
          JSON.stringify({
            route_discovery_update: {
              timestamp: new Date(),
              action: 'loading_location_confirmed',
              previous_location_id: assignment.loading_location_id,
              new_location: { id: location.id, name: location.name },
              discovery_type: 'actual_vs_predicted'
            }
          }),
          assignment.id
        ];
        description = `Loading location updated: ${location.name} (different from prediction)`;
        needsUpdate = true;
      } else {
        description = `Loading location confirmed: ${location.name} (matched prediction)`;
      }
    } else if (location.type === 'unloading' && confirmedLocation.type !== 'unloading') {
      // Truck visited unloading location - update if different from predicted
      if (assignment.unloading_location_id !== location.id) {
        updateQuery = `
          UPDATE assignments
          SET unloading_location_id = $1, updated_at = $2,
              notes = COALESCE(notes::jsonb, '{}'::jsonb) || $3::jsonb
          WHERE id = $4
          RETURNING *
        `;
        updateParams = [
          location.id,
          new Date(),
          JSON.stringify({
            route_discovery_update: {
              timestamp: new Date(),
              action: 'unloading_location_confirmed',
              previous_location_id: assignment.unloading_location_id,
              new_location: { id: location.id, name: location.name },
              discovery_type: 'actual_vs_predicted'
            }
          }),
          assignment.id
        ];
        description = `Unloading location updated: ${location.name} (different from prediction)`;
        needsUpdate = true;
      } else {
        description = `Unloading location confirmed: ${location.name} (matched prediction)`;
      }
    } else {
      // Location visit doesn't change the assignment (same type as confirmed location)
      this.logger.log(`ℹ️  Location visit doesn't require assignment update: ${location.name}`);
      return assignment;
    }

    if (needsUpdate) {
      const updateResult = await client.query(updateQuery, updateParams);
      this.logger.log(`✅ ${description}`);

      // Send WebSocket notification for route update
      try {
        const previousLocationQuery = await client.query('SELECT name FROM locations WHERE id = $1', [
          location.type === 'loading' ? assignment.loading_location_id : assignment.unloading_location_id
        ]);
        const previousLocation = previousLocationQuery.rows[0];

        notifyRouteUpdated({
          id: assignment.id,
          assignment_code: assignment.assignment_code,
          truck_number: assignment.truck_number || 'Unknown'
        }, previousLocation, location, location.type);
      } catch (error) {
        this.logger.log(`⚠️  Failed to send route update notification: ${error.message}`);
      }

      return updateResult.rows[0];
    } else {
      this.logger.log(`✅ ${description}`);

      // Send WebSocket notification for location confirmation (no change needed)
      try {
        notifyRouteLocationConfirmed({
          id: assignment.id,
          assignment_code: assignment.assignment_code,
          truck_number: assignment.truck_number || 'Unknown'
        }, location, location.type);
      } catch (error) {
        this.logger.log(`⚠️  Failed to send location confirmation notification: ${error.message}`);
      }

      // Check if route discovery is now complete (both locations confirmed)
      const hasLoadingLocation = assignment.loading_location_id !== null;
      const hasUnloadingLocation = assignment.unloading_location_id !== null;

      if (hasLoadingLocation && hasUnloadingLocation) {
        // Route discovery is complete - send completion notification
        try {
          notifyRouteDiscoveryCompleted({
            id: assignment.id,
            assignment_code: assignment.assignment_code,
            truck_number: assignment.truck_number || 'Unknown'
          }, {
            loading_location: assignment.loading_location_name || 'Unknown',
            unloading_location: assignment.unloading_location_name || 'Unknown'
          });
          this.logger.log(`🎯 Route discovery completed for assignment ${assignment.assignment_code}`);
        } catch (error) {
          this.logger.log(`⚠️  Failed to send route discovery completion notification: ${error.message}`);
        }
      }

      return assignment;
    }
  }

  /**
   * Handle post-completion loading scenarios for multi-location workflows
   * @param {Object} completedTrip - Recently completed trip
   * @param {Object} newLocation - New loading location
   * @param {number} truckId - Truck ID
   * @param {number} driverId - Driver ID
   * @returns {Promise<Object>} Created assignment and trip
   */
  async handlePostCompletionLoading(completedTrip, newLocation, truckId, driverId) {
    this.logger.log(`🔄 Handling post-completion loading for truck ${truckId} at ${newLocation.name}`);

    // Determine workflow type
    const workflowType = this.determineWorkflowType(completedTrip, newLocation);

    if (workflowType === 'extended') {
      // Create A→B→C extension
      return await this.createExtendedWorkflow(completedTrip, newLocation, truckId, driverId);
    } else if (workflowType === 'cycle') {
      // Create C→B→C cycle
      return await this.createCycleWorkflow(completedTrip, newLocation, truckId, driverId);
    }

    return null;
  }

  /**
   * Determine workflow type based on completed trip and new location
   * @param {Object} completedTrip - Recently completed trip
   * @param {Object} newLocation - New location
   * @returns {string} Workflow type: 'extended', 'cycle', or 'none'
   */
  determineWorkflowType(completedTrip, newLocation) {
    // If new location is different from both previous loading and unloading locations
    if (newLocation.id !== completedTrip.loading_location_id &&
        newLocation.id !== completedTrip.unloading_location_id) {
      return 'extended'; // A→B→C extension
    }

    // If new location is the same as previous unloading location
    if (newLocation.id === completedTrip.unloading_location_id) {
      return 'cycle'; // C→B→C cycle (loading at previous unloading location)
    }

    return 'none'; // Standard workflow, no extension needed
  }

  /**
   * Create workflow trip with proper metadata
   * @param {Object} params - Trip creation parameters
   * @returns {Promise<Object>} Created trip
   */
  async createWorkflowTrip(params) {
    const {
      assignment_id,
      truck_id,
      driver_id,
      baseline_trip_id,
      workflow_type,
      cycle_number = 1,
      client
    } = params;

    // Get next trip number for this assignment
    const tripNumberResult = await client.query(`
      SELECT COALESCE(MAX(trip_number), 0) + 1 as next_trip_number
      FROM trip_logs
      WHERE assignment_id = $1
    `, [assignment_id]);

    const tripNumber = tripNumberResult.rows[0].next_trip_number;

    // Create new trip with workflow metadata
    const newTripResult = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, is_extended_trip,
        workflow_type, baseline_trip_id, cycle_number,
        created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `, [
      assignment_id,
      tripNumber,
      'assigned',
      true,
      workflow_type,
      baseline_trip_id,
      cycle_number,
      new Date(),
      new Date()
    ]);

    return newTripResult.rows[0];
  }
}

module.exports = { AutoAssignmentCreator };