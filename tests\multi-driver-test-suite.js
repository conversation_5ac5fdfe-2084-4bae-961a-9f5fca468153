/**
 * Multi-Driver Test Suite
 * Comprehensive testing for shift-based multi-driver scenarios
 * Validates 4-phase workflow integrity
 */

const { query, getClient } = require('../server/config/database');
const ScannerShiftIntegration = require('../server/utils/ScannerShiftIntegration');
const ShiftAwareAssignmentValidator = require('../server/utils/ShiftAwareAssignmentValidator');

class MultiDriverTestSuite {
  constructor() {
    this.shiftIntegration = new ScannerShiftIntegration();
    this.shiftValidator = new ShiftAwareAssignmentValidator();
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🚀 Starting Multi-Driver Test Suite...\n');

    try {
      // Test 1: Basic shift assignment
      await this.testBasicShiftAssignment();
      
      // Test 2: Shift handover during active trip
      await this.testShiftHandoverDuringTrip();
      
      // Test 3: 4-phase workflow with shift changes
      await this.testFourPhaseWorkflowWithShifts();
      
      // Test 4: Auto-assignment creation with shifts
      await this.testAutoAssignmentWithShifts();
      
      // Test 5: Multiple drivers per truck validation
      await this.testMultipleDriversPerTruck();

      // Print results
      this.printTestResults();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  async testBasicShiftAssignment() {
    const testName = 'Basic Shift Assignment';
    console.log(`📋 Testing: ${testName}`);

    try {
      const client = await getClient();
      
      // Setup: Create test shift
      const shiftResult = await client.query(`
        INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
        VALUES (1, 1, 'day', CURRENT_DATE, '06:00:00', '18:00:00', 'active')
        RETURNING id
      `);
      
      const shiftId = shiftResult.rows[0].id;

      // Test: Get current driver assignment
      const driverAssignment = await this.shiftValidator.getCurrentDriverAssignment(client, 1);
      
      // Validate
      const success = driverAssignment && driverAssignment.driver.id === 1;
      
      // Cleanup
      await client.query('DELETE FROM driver_shifts WHERE id = $1', [shiftId]);
      client.release();

      this.addTestResult(testName, success, success ? 'Shift assignment working correctly' : 'Failed to get shift assignment');

    } catch (error) {
      this.addTestResult(testName, false, `Error: ${error.message}`);
    }
  }

  async testShiftHandoverDuringTrip() {
    const testName = 'Shift Handover During Active Trip';
    console.log(`📋 Testing: ${testName}`);

    try {
      const client = await getClient();
      
      // Setup: Create shifts and trip
      const shift1Result = await client.query(`
        INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
        VALUES (1, 1, 'day', CURRENT_DATE, '06:00:00', '18:00:00', 'active')
        RETURNING id
      `);
      
      const shift2Result = await client.query(`
        INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
        VALUES (1, 2, 'night', CURRENT_DATE, '18:00:00', '06:00:00', 'scheduled')
        RETURNING id
      `);

      const shift1Id = shift1Result.rows[0].id;
      const shift2Id = shift2Result.rows[0].id;

      // Create mock trip
      const tripResult = await client.query(`
        INSERT INTO trip_logs (assignment_id, trip_number, status, loading_start_time)
        VALUES (1, 1, 'loading_end', CURRENT_TIMESTAMP)
        RETURNING id
      `);
      
      const tripId = tripResult.rows[0].id;

      // Test: Handle shift handover
      const handoverId = await this.shiftValidator.handleShiftHandover(
        client, 1, shift1Id, shift2Id, tripId
      );

      // Validate handover was created
      const handoverCheck = await client.query(
        'SELECT id FROM shift_handovers WHERE id = $1', 
        [handoverId]
      );

      const success = handoverCheck.rows.length > 0;

      // Cleanup
      await client.query('DELETE FROM shift_handovers WHERE id = $1', [handoverId]);
      await client.query('DELETE FROM trip_logs WHERE id = $1', [tripId]);
      await client.query('DELETE FROM driver_shifts WHERE id IN ($1, $2)', [shift1Id, shift2Id]);
      client.release();

      this.addTestResult(testName, success, success ? 'Shift handover completed successfully' : 'Failed to create shift handover');

    } catch (error) {
      this.addTestResult(testName, false, `Error: ${error.message}`);
    }
  }

  async testFourPhaseWorkflowWithShifts() {
    const testName = '4-Phase Workflow with Shift Changes';
    console.log(`📋 Testing: ${testName}`);

    try {
      const client = await getClient();
      
      // Setup: Create shift and assignment
      const shiftResult = await client.query(`
        INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
        VALUES (1, 1, 'day', CURRENT_DATE, '06:00:00', '18:00:00', 'active')
        RETURNING id
      `);
      
      const shiftId = shiftResult.rows[0].id;

      // Create shift assignment
      const assignmentId = await this.shiftValidator.createShiftAssignment(
        client, 1, shiftId, 1, 2
      );

      // Test: Validate assignment works with 4-phase workflow
      const assignment = await this.shiftIntegration.getValidAssignmentForTruck(
        client, 
        { id: 1, truck_number: 'DT-100' }, 
        { id: 1, name: 'Point A', type: 'loading' }
      );

      // Validate assignment is compatible with 4-phase workflow
      const success = assignment && 
                     assignment.id === assignmentId && 
                     assignment.loading_location_id === 1 &&
                     assignment.unloading_location_id === 2;

      // Cleanup
      await client.query('DELETE FROM assignments WHERE id = $1', [assignmentId]);
      await client.query('DELETE FROM driver_shifts WHERE id = $1', [shiftId]);
      client.release();

      this.addTestResult(testName, success, success ? '4-phase workflow compatible with shifts' : 'Workflow compatibility failed');

    } catch (error) {
      this.addTestResult(testName, false, `Error: ${error.message}`);
    }
  }

  async testAutoAssignmentWithShifts() {
    const testName = 'Auto-Assignment Creation with Shifts';
    console.log(`📋 Testing: ${testName}`);

    try {
      const client = await getClient();
      
      // Setup: Create active shift
      const shiftResult = await client.query(`
        INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
        VALUES (1, 1, 'day', CURRENT_DATE, '06:00:00', '18:00:00', 'active')
        RETURNING id
      `);
      
      const shiftId = shiftResult.rows[0].id;

      // Test: Create shift-aware auto-assignment
      const assignmentId = await this.shiftValidator.createShiftAwareAssignment(
        client, 1, 1, 2
      );

      // Validate assignment was created
      const assignmentCheck = await client.query(`
        SELECT id, shift_id, is_shift_assignment 
        FROM assignments 
        WHERE id = $1
      `, [assignmentId]);

      const success = assignmentCheck.rows.length > 0 && 
                     assignmentCheck.rows[0].shift_id === shiftId &&
                     assignmentCheck.rows[0].is_shift_assignment === true;

      // Cleanup
      if (assignmentId) {
        await client.query('DELETE FROM assignments WHERE id = $1', [assignmentId]);
      }
      await client.query('DELETE FROM driver_shifts WHERE id = $1', [shiftId]);
      client.release();

      this.addTestResult(testName, success, success ? 'Auto-assignment with shifts working' : 'Auto-assignment failed');

    } catch (error) {
      this.addTestResult(testName, false, `Error: ${error.message}`);
    }
  }

  async testMultipleDriversPerTruck() {
    const testName = 'Multiple Drivers Per Truck Validation';
    console.log(`📋 Testing: ${testName}`);

    try {
      const client = await getClient();
      
      // Setup: Create day and night shifts for same truck
      const dayShiftResult = await client.query(`
        INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
        VALUES (1, 1, 'day', CURRENT_DATE, '06:00:00', '18:00:00', 'active')
        RETURNING id
      `);
      
      const nightShiftResult = await client.query(`
        INSERT INTO driver_shifts (truck_id, driver_id, shift_type, shift_date, start_time, end_time, status)
        VALUES (1, 2, 'night', CURRENT_DATE, '18:00:00', '06:00:00', 'scheduled')
        RETURNING id
      `);

      const dayShiftId = dayShiftResult.rows[0].id;
      const nightShiftId = nightShiftResult.rows[0].id;

      // Test: Validate only active shift is returned
      const currentDriver = await this.shiftValidator.getCurrentActiveShift(client, 1);
      
      // Validate correct driver is active
      const success = currentDriver && 
                     currentDriver.driver_id === 1 && 
                     currentDriver.shift_type === 'day';

      // Cleanup
      await client.query('DELETE FROM driver_shifts WHERE id IN ($1, $2)', [dayShiftId, nightShiftId]);
      client.release();

      this.addTestResult(testName, success, success ? 'Multiple drivers per truck working correctly' : 'Multiple driver validation failed');

    } catch (error) {
      this.addTestResult(testName, false, `Error: ${error.message}`);
    }
  }

  addTestResult(testName, success, message) {
    this.testResults.push({
      test: testName,
      success,
      message,
      timestamp: new Date().toISOString()
    });

    const status = success ? '✅ PASS' : '❌ FAIL';
    console.log(`   ${status}: ${message}\n`);
  }

  printTestResults() {
    console.log('\n📊 Test Results Summary:');
    console.log('=' .repeat(50));
    
    const passed = this.testResults.filter(r => r.success).length;
    const total = this.testResults.length;
    
    this.testResults.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.test}: ${result.message}`);
    });
    
    console.log('=' .repeat(50));
    console.log(`📈 Overall: ${passed}/${total} tests passed (${((passed/total)*100).toFixed(1)}%)`);
    
    if (passed === total) {
      console.log('🎉 All tests passed! Multi-driver system is ready for implementation.');
    } else {
      console.log('⚠️  Some tests failed. Please review before implementation.');
    }
  }
}

// Export for use in other test files
module.exports = MultiDriverTestSuite;

// Run tests if called directly
if (require.main === module) {
  const testSuite = new MultiDriverTestSuite();
  testSuite.runAllTests().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Test suite execution failed:', error);
    process.exit(1);
  });
}
