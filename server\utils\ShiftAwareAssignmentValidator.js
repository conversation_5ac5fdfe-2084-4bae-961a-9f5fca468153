/**
 * Shift-Aware Assignment Validator
 * Enhances existing assignment validation with multi-driver shift support
 * Maintains full compatibility with 4-phase workflow
 */

const { query } = require('../config/database');

class ShiftAwareAssignmentValidator {
  
  /**
   * Get current active driver and assignment for a truck
   * This replaces the simple truck-driver lookup with shift-aware logic
   */
  async getCurrentDriverAssignment(client, truckId, locationId = null) {
    try {
      // First, try to get current shift-based assignment
      const shiftAssignmentQuery = `
        WITH current_shift AS (
          SELECT 
            ds.id as shift_id,
            ds.driver_id,
            ds.shift_type,
            ds.start_time,
            ds.end_time,
            ds.assignment_id,
            d.full_name as driver_name,
            d.employee_id
          FROM driver_shifts ds
          JOIN drivers d ON ds.driver_id = d.id
          WHERE ds.truck_id = $1
            AND ds.status = 'active'
            AND ds.shift_date = CURRENT_DATE
            AND CURRENT_TIME BETWEEN ds.start_time AND 
                CASE 
                  WHEN ds.end_time < ds.start_time 
                  THEN ds.end_time + interval '24 hours'
                  ELSE ds.end_time 
                END
          ORDER BY ds.created_at DESC
          LIMIT 1
        ),
        shift_assignment AS (
          SELECT 
            a.id as assignment_id,
            a.assignment_code,
            a.truck_id,
            a.driver_id,
            a.loading_location_id,
            a.unloading_location_id,
            a.status as assignment_status,
            a.priority,
            a.expected_loads_per_day,
            a.shift_id,
            a.is_shift_assignment,
            ll.name as loading_location_name,
            ul.name as unloading_location_name,
            cs.shift_id,
            cs.shift_type,
            cs.driver_name,
            cs.employee_id
          FROM current_shift cs
          LEFT JOIN assignments a ON cs.assignment_id = a.id
          LEFT JOIN locations ll ON a.loading_location_id = ll.id
          LEFT JOIN locations ul ON a.unloading_location_id = ul.id
          WHERE a.status = 'assigned'
        ),
        fallback_assignment AS (
          SELECT 
            a.id as assignment_id,
            a.assignment_code,
            a.truck_id,
            a.driver_id,
            a.loading_location_id,
            a.unloading_location_id,
            a.status as assignment_status,
            a.priority,
            a.expected_loads_per_day,
            null as shift_id,
            false as is_shift_assignment,
            ll.name as loading_location_name,
            ul.name as unloading_location_name,
            null as shift_type,
            d.full_name as driver_name,
            d.employee_id
          FROM assignments a
          JOIN drivers d ON a.driver_id = d.id
          LEFT JOIN locations ll ON a.loading_location_id = ll.id
          LEFT JOIN locations ul ON a.unloading_location_id = ul.id
          WHERE a.truck_id = $1
            AND a.status = 'assigned'
            AND (a.shift_id IS NULL OR a.is_shift_assignment = false)
          ORDER BY a.created_at DESC
          LIMIT 1
        )
        SELECT * FROM shift_assignment
        UNION ALL
        SELECT * FROM fallback_assignment
        LIMIT 1
      `;

      const result = await client.query(shiftAssignmentQuery, [truckId]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const assignment = result.rows[0];
      
      return {
        assignment: {
          id: assignment.assignment_id,
          assignment_code: assignment.assignment_code,
          truck_id: assignment.truck_id,
          driver_id: assignment.driver_id,
          loading_location_id: assignment.loading_location_id,
          unloading_location_id: assignment.unloading_location_id,
          status: assignment.assignment_status,
          priority: assignment.priority,
          expected_loads_per_day: assignment.expected_loads_per_day,
          loading_location_name: assignment.loading_location_name,
          unloading_location_name: assignment.unloading_location_name,
          shift_id: assignment.shift_id,
          is_shift_assignment: assignment.is_shift_assignment
        },
        driver: {
          id: assignment.driver_id,
          name: assignment.driver_name,
          employee_id: assignment.employee_id
        },
        shift: assignment.shift_id ? {
          id: assignment.shift_id,
          type: assignment.shift_type
        } : null
      };

    } catch (error) {
      console.error('Error getting current driver assignment:', error);
      throw error;
    }
  }

  /**
   * Create shift-aware auto-assignment
   * Integrates with existing AutoAssignmentCreator
   */
  async createShiftAwareAssignment(client, truckId, loadingLocationId, unloadingLocationId) {
    try {
      // Get current active shift
      const currentShift = await this.getCurrentActiveShift(client, truckId);
      
      if (currentShift) {
        // Create shift-based assignment
        const assignmentId = await this.createShiftAssignment(
          client, 
          truckId, 
          currentShift.shift_id, 
          loadingLocationId, 
          unloadingLocationId
        );
        
        return assignmentId;
      } else {
        // Fall back to regular assignment creation
        // This maintains compatibility with existing AutoAssignmentCreator
        return null;
      }
      
    } catch (error) {
      console.error('Error creating shift-aware assignment:', error);
      throw error;
    }
  }

  /**
   * Get current active shift for a truck
   */
  async getCurrentActiveShift(client, truckId) {
    const query_text = `
      SELECT 
        ds.id as shift_id,
        ds.driver_id,
        ds.shift_type,
        ds.start_time,
        ds.end_time,
        d.full_name as driver_name,
        d.employee_id
      FROM driver_shifts ds
      JOIN drivers d ON ds.driver_id = d.id
      WHERE ds.truck_id = $1
        AND ds.status = 'active'
        AND ds.shift_date = CURRENT_DATE
        AND CURRENT_TIME BETWEEN ds.start_time AND 
            CASE 
              WHEN ds.end_time < ds.start_time 
              THEN ds.end_time + interval '24 hours'
              ELSE ds.end_time 
            END
      ORDER BY ds.created_at DESC
      LIMIT 1
    `;

    const result = await client.query(query_text, [truckId]);
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  /**
   * Create assignment for a specific shift
   */
  async createShiftAssignment(client, truckId, shiftId, loadingLocationId, unloadingLocationId) {
    const query_text = `
      SELECT create_shift_assignment($1, $2, $3, $4) as assignment_id
    `;

    const result = await client.query(query_text, [truckId, shiftId, loadingLocationId, unloadingLocationId]);
    return result.rows[0].assignment_id;
  }

  /**
   * Handle shift handover during active trip
   * This is called when a shift change occurs during an active trip
   */
  async handleShiftHandover(client, truckId, outgoingShiftId, incomingShiftId, activeTripId = null) {
    try {
      // Get trip context if active
      let tripContext = null;
      if (activeTripId) {
        const tripQuery = `
          SELECT 
            tl.id,
            tl.status,
            tl.actual_loading_location_id,
            tl.actual_unloading_location_id,
            a.loading_location_id,
            a.unloading_location_id
          FROM trip_logs tl
          JOIN assignments a ON tl.assignment_id = a.id
          WHERE tl.id = $1
        `;
        
        const tripResult = await client.query(tripQuery, [activeTripId]);
        if (tripResult.rows.length > 0) {
          tripContext = tripResult.rows[0];
        }
      }

      // Create handover record
      const handoverQuery = `
        INSERT INTO shift_handovers (
          truck_id, outgoing_shift_id, incoming_shift_id,
          active_trip_id, trip_status_at_handover,
          location_at_handover, handover_notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id
      `;

      const handoverResult = await client.query(handoverQuery, [
        truckId,
        outgoingShiftId,
        incomingShiftId,
        activeTripId,
        tripContext?.status || null,
        tripContext?.actual_loading_location_id || tripContext?.loading_location_id || null,
        'Automatic shift handover during active trip'
      ]);

      // Update shift statuses
      await client.query(`
        UPDATE driver_shifts 
        SET status = 'completed', updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [outgoingShiftId]);

      await client.query(`
        UPDATE driver_shifts 
        SET status = 'active', updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `, [incomingShiftId]);

      return handoverResult.rows[0].id;

    } catch (error) {
      console.error('Error handling shift handover:', error);
      throw error;
    }
  }

  /**
   * Validate assignment compatibility with current shift
   * This ensures assignments are valid for the current driver shift
   */
  async validateShiftAssignmentCompatibility(client, assignmentId, truckId) {
    try {
      const query_text = `
        WITH current_shift AS (
          SELECT ds.id, ds.driver_id, ds.shift_type
          FROM driver_shifts ds
          WHERE ds.truck_id = $2
            AND ds.status = 'active'
            AND ds.shift_date = CURRENT_DATE
            AND CURRENT_TIME BETWEEN ds.start_time AND 
                CASE 
                  WHEN ds.end_time < ds.start_time 
                  THEN ds.end_time + interval '24 hours'
                  ELSE ds.end_time 
                END
          LIMIT 1
        )
        SELECT 
          a.id,
          a.driver_id,
          a.shift_id,
          a.is_shift_assignment,
          cs.driver_id as current_shift_driver_id,
          cs.shift_type,
          CASE 
            WHEN a.is_shift_assignment = true AND a.shift_id IS NOT NULL THEN 'shift_assignment'
            WHEN a.driver_id = cs.driver_id THEN 'driver_match'
            WHEN cs.driver_id IS NULL THEN 'no_active_shift'
            ELSE 'driver_mismatch'
          END as compatibility_status
        FROM assignments a
        LEFT JOIN current_shift cs ON true
        WHERE a.id = $1
      `;

      const result = await client.query(query_text, [assignmentId, truckId]);
      
      if (result.rows.length === 0) {
        return { valid: false, reason: 'Assignment not found' };
      }

      const assignment = result.rows[0];
      
      switch (assignment.compatibility_status) {
        case 'shift_assignment':
        case 'driver_match':
          return { valid: true, reason: 'Assignment compatible with current shift' };
        case 'no_active_shift':
          return { valid: true, reason: 'No active shift - using assignment driver' };
        case 'driver_mismatch':
          return { 
            valid: false, 
            reason: `Assignment driver mismatch. Assignment for driver ${assignment.driver_id}, but current shift driver is ${assignment.current_shift_driver_id}` 
          };
        default:
          return { valid: false, reason: 'Unknown compatibility status' };
      }

    } catch (error) {
      console.error('Error validating shift assignment compatibility:', error);
      throw error;
    }
  }
}

module.exports = ShiftAwareAssignmentValidator;
