-- ============================================================================
-- Migration 020: Global Unique Trip Numbers
-- Purpose: Implement globally unique trip numbers to avoid duplication
-- Date: 2025-01-08
-- ============================================================================

-- Remove the old constraint that limited uniqueness to per-assignment
ALTER TABLE trip_logs DROP CONSTRAINT IF EXISTS trip_logs_assignment_id_trip_number_key;

-- Add new constraint for globally unique trip numbers
ALTER TABLE trip_logs ADD CONSTRAINT unique_global_trip_number UNIQUE (trip_number);

-- Update existing trip numbers to be globally unique
-- This will use the ID + offset to ensure uniqueness
DO $$
DECLARE
    trip_record RECORD;
    new_trip_number INTEGER;
    max_id INTEGER;
BEGIN
    -- Get the maximum ID to use as offset
    SELECT COALESCE(MAX(id), 0) INTO max_id FROM trip_logs;
    
    -- Update each trip with a globally unique number
    FOR trip_record IN 
        SELECT id, trip_number, assignment_id 
        FROM trip_logs 
        ORDER BY id ASC
    LOOP
        -- Calculate new globally unique trip number
        new_trip_number := trip_record.id;
        
        -- Update the trip number
        UPDATE trip_logs 
        SET trip_number = new_trip_number 
        WHERE id = trip_record.id;
        
        RAISE NOTICE 'Updated trip ID % from trip_number % to %', 
            trip_record.id, trip_record.trip_number, new_trip_number;
    END LOOP;
    
    RAISE NOTICE 'Migration completed. Updated % trips with globally unique numbers.', 
        (SELECT COUNT(*) FROM trip_logs);
END $$;

-- Create index for performance on the new unique constraint
CREATE INDEX IF NOT EXISTS idx_trip_logs_global_trip_number ON trip_logs (trip_number);

-- Add comment to document the change
COMMENT ON COLUMN trip_logs.trip_number IS 'Globally unique trip number (equals trip ID for uniqueness)';

-- Verify the migration worked
DO $$
DECLARE
    duplicate_count INTEGER;
    total_trips INTEGER;
BEGIN
    -- Check for any remaining duplicates
    SELECT COUNT(*) INTO duplicate_count
    FROM (
        SELECT trip_number, COUNT(*) as cnt
        FROM trip_logs
        GROUP BY trip_number
        HAVING COUNT(*) > 1
    ) duplicates;
    
    SELECT COUNT(*) INTO total_trips FROM trip_logs;
    
    IF duplicate_count > 0 THEN
        RAISE EXCEPTION 'Migration failed: % duplicate trip numbers found', duplicate_count;
    ELSE
        RAISE NOTICE 'Migration successful: % trips with unique trip numbers, no duplicates found', total_trips;
    END IF;
END $$;
