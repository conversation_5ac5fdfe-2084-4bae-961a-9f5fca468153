/**
 * Trip Number Generator Utility
 * Provides globally unique trip numbers to eliminate duplication
 */

class TripNumberGenerator {
  
  /**
   * Generate a globally unique trip number
   * Uses database ID as trip number to ensure uniqueness across all assignments
   * 
   * @param {Object} client - Database client
   * @param {number} assignmentId - Assignment ID
   * @returns {Promise<number>} Globally unique trip number
   */
  static async getNextTripNumber(client, assignmentId) {
    try {
      // Method 1: Use database sequence for global uniqueness
      // This is more efficient than the placeholder method
      const sequenceResult = await client.query(`
        SELECT nextval('trip_logs_id_seq') as next_id
      `);
      
      const uniqueTripNumber = sequenceResult.rows[0].next_id;
      
      // Reset the sequence to the correct value since we're using it for trip numbers
      await client.query(`
        SELECT setval('trip_logs_id_seq', $1, false)
      `, [uniqueTripNumber]);
      
      return uniqueTripNumber;
      
    } catch (sequenceError) {
      console.warn('Sequence method failed, falling back to placeholder method:', sequenceError.message);
      
      // Method 2: Fallback to placeholder method if sequence fails
      return await this.getNextTripNumberFallback(client, assignmentId);
    }
  }
  
  /**
   * Fallback method using placeholder insertion
   * 
   * @param {Object} client - Database client
   * @param {number} assignmentId - Assignment ID
   * @returns {Promise<number>} Globally unique trip number
   */
  static async getNextTripNumberFallback(client, assignmentId) {
    // Create a placeholder trip to get the next ID
    const placeholderResult = await client.query(`
      INSERT INTO trip_logs (assignment_id, trip_number, status, created_at, updated_at)
      VALUES ($1, 0, 'placeholder', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING id
    `, [assignmentId]);
    
    const tripId = placeholderResult.rows[0].id;
    const uniqueTripNumber = tripId; // Use ID as trip number for global uniqueness
    
    // Delete the placeholder (we'll create the real trip later)
    await client.query(`
      DELETE FROM trip_logs WHERE id = $1
    `, [tripId]);
    
    return uniqueTripNumber;
  }
  
  /**
   * Get the next available trip number without creating a trip
   * Useful for validation or preview purposes
   * 
   * @param {Object} client - Database client
   * @returns {Promise<number>} Next available trip number
   */
  static async peekNextTripNumber(client) {
    try {
      // Get the current sequence value without incrementing
      const result = await client.query(`
        SELECT last_value + 1 as next_value 
        FROM trip_logs_id_seq
      `);
      
      return result.rows[0].next_value;
      
    } catch (error) {
      // Fallback: get max ID + 1
      const fallbackResult = await client.query(`
        SELECT COALESCE(MAX(id), 0) + 1 as next_value 
        FROM trip_logs
      `);
      
      return fallbackResult.rows[0].next_value;
    }
  }
  
  /**
   * Validate that a trip number is unique globally
   * 
   * @param {Object} client - Database client
   * @param {number} tripNumber - Trip number to validate
   * @returns {Promise<boolean>} True if unique, false if duplicate
   */
  static async isUnique(client, tripNumber) {
    const result = await client.query(`
      SELECT COUNT(*) as count 
      FROM trip_logs 
      WHERE trip_number = $1
    `, [tripNumber]);
    
    return parseInt(result.rows[0].count) === 0;
  }
  
  /**
   * Get statistics about trip number usage
   * 
   * @param {Object} client - Database client
   * @returns {Promise<Object>} Statistics object
   */
  static async getStatistics(client) {
    const result = await client.query(`
      SELECT 
        COUNT(*) as total_trips,
        COUNT(DISTINCT trip_number) as unique_trip_numbers,
        COUNT(*) - COUNT(DISTINCT trip_number) as duplicates,
        MIN(trip_number) as min_trip_number,
        MAX(trip_number) as max_trip_number
      FROM trip_logs
      WHERE trip_number > 0
    `);
    
    const stats = result.rows[0];
    
    return {
      totalTrips: parseInt(stats.total_trips),
      uniqueTripNumbers: parseInt(stats.unique_trip_numbers),
      duplicates: parseInt(stats.duplicates),
      minTripNumber: parseInt(stats.min_trip_number) || 0,
      maxTripNumber: parseInt(stats.max_trip_number) || 0,
      hasNoDuplicates: parseInt(stats.duplicates) === 0
    };
  }
  
  /**
   * Fix any existing duplicate trip numbers
   * This is a one-time migration utility
   * 
   * @param {Object} client - Database client
   * @returns {Promise<Object>} Migration results
   */
  static async fixDuplicates(client) {
    const stats = await this.getStatistics(client);
    
    if (stats.hasNoDuplicates) {
      return {
        success: true,
        message: 'No duplicates found',
        updatedTrips: 0
      };
    }
    
    // Get all trips ordered by ID
    const trips = await client.query(`
      SELECT id, trip_number, assignment_id
      FROM trip_logs
      ORDER BY id ASC
    `);
    
    let updatedCount = 0;
    
    // Update each trip to use its ID as trip number
    for (const trip of trips.rows) {
      if (trip.trip_number !== trip.id) {
        await client.query(`
          UPDATE trip_logs 
          SET trip_number = $1 
          WHERE id = $2
        `, [trip.id, trip.id]);
        
        updatedCount++;
      }
    }
    
    return {
      success: true,
      message: `Fixed ${updatedCount} trips with duplicate numbers`,
      updatedTrips: updatedCount
    };
  }
}

module.exports = TripNumberGenerator;
