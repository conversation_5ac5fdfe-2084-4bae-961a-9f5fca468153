/**
 * Scanner Shift Integration
 * Minimal modifications to scanner.js for multi-driver support
 * Preserves 4-phase workflow integrity
 */

const ShiftAwareAssignmentValidator = require('./ShiftAwareAssignmentValidator');

class ScannerShiftIntegration {
  constructor() {
    this.shiftValidator = new ShiftAwareAssignmentValidator();
  }

  /**
   * Enhanced assignment validation for scanner.js
   * This replaces the existing assignment lookup in processTruckScan
   * 
   * Usage in scanner.js:
   * const shiftIntegration = new ScannerShiftIntegration();
   * const validAssignment = await shiftIntegration.getValidAssignmentForTruck(client, truck, location);
   */
  async getValidAssignmentForTruck(client, truck, location) {
    try {
      // Step 1: Get current driver assignment (shift-aware)
      const driverAssignment = await this.shiftValidator.getCurrentDriverAssignment(
        client, 
        truck.id, 
        location.id
      );

      if (!driverAssignment) {
        return null; // No assignment found - will trigger auto-assignment creation
      }

      // Step 2: Validate assignment compatibility
      const compatibility = await this.shiftValidator.validateShiftAssignmentCompatibility(
        client,
        driverAssignment.assignment.id,
        truck.id
      );

      if (!compatibility.valid) {
        console.log(`Assignment compatibility check failed: ${compatibility.reason}`);
        return null; // Will trigger auto-assignment creation
      }

      // Step 3: Return valid assignment in expected format
      return {
        id: driverAssignment.assignment.id,
        assignment_code: driverAssignment.assignment.assignment_code,
        truck_id: driverAssignment.assignment.truck_id,
        driver_id: driverAssignment.assignment.driver_id,
        loading_location_id: driverAssignment.assignment.loading_location_id,
        unloading_location_id: driverAssignment.assignment.unloading_location_id,
        status: driverAssignment.assignment.status,
        priority: driverAssignment.assignment.priority,
        expected_loads_per_day: driverAssignment.assignment.expected_loads_per_day,
        loading_location_name: driverAssignment.assignment.loading_location_name,
        unloading_location_name: driverAssignment.assignment.unloading_location_name,
        
        // Additional shift context (for logging/debugging)
        shift_context: {
          driver_name: driverAssignment.driver.name,
          employee_id: driverAssignment.driver.employee_id,
          shift_id: driverAssignment.shift?.id || null,
          shift_type: driverAssignment.shift?.type || null,
          is_shift_assignment: driverAssignment.assignment.is_shift_assignment
        }
      };

    } catch (error) {
      console.error('Error getting valid assignment for truck:', error);
      throw error;
    }
  }

  /**
   * Enhanced auto-assignment creation for scanner.js
   * This integrates with the existing AutoAssignmentCreator
   * 
   * Usage in scanner.js:
   * const autoAssignment = await shiftIntegration.createShiftAwareAutoAssignment(
   *   client, truck, location, existingAutoAssignmentCreator
   * );
   */
  async createShiftAwareAutoAssignment(client, truck, location, autoAssignmentCreator) {
    try {
      // Step 1: Try shift-aware assignment creation
      const shiftAssignmentId = await this.shiftValidator.createShiftAwareAssignment(
        client,
        truck.id,
        location.id,
        location.id // For now, use same location for both loading and unloading
      );

      if (shiftAssignmentId) {
        // Get the created assignment details
        const assignmentQuery = `
          SELECT 
            a.*,
            ll.name as loading_location_name,
            ul.name as unloading_location_name,
            d.full_name as driver_name,
            d.employee_id
          FROM assignments a
          LEFT JOIN locations ll ON a.loading_location_id = ll.id
          LEFT JOIN locations ul ON a.unloading_location_id = ul.id
          LEFT JOIN drivers d ON a.driver_id = d.id
          WHERE a.id = $1
        `;

        const result = await client.query(assignmentQuery, [shiftAssignmentId]);
        
        if (result.rows.length > 0) {
          const assignment = result.rows[0];
          return {
            id: assignment.id,
            assignment_code: assignment.assignment_code,
            truck_id: assignment.truck_id,
            driver_id: assignment.driver_id,
            loading_location_id: assignment.loading_location_id,
            unloading_location_id: assignment.unloading_location_id,
            status: assignment.status,
            priority: assignment.priority,
            expected_loads_per_day: assignment.expected_loads_per_day,
            loading_location_name: assignment.loading_location_name,
            unloading_location_name: assignment.unloading_location_name,
            
            // Shift context
            shift_context: {
              driver_name: assignment.driver_name,
              employee_id: assignment.employee_id,
              is_shift_assignment: assignment.is_shift_assignment,
              auto_created: true
            }
          };
        }
      }

      // Step 2: Fall back to existing AutoAssignmentCreator
      // This maintains full compatibility with existing logic
      const shouldCreate = await autoAssignmentCreator.shouldCreateAutoAssignment({
        truck,
        location,
        client
      });

      if (shouldCreate.shouldCreate) {
        return await autoAssignmentCreator.createAutoAssignment({
          truck,
          location,
          client,
          strategy: shouldCreate.strategy,
          confidence: shouldCreate.confidence
        });
      }

      return null;

    } catch (error) {
      console.error('Error creating shift-aware auto assignment:', error);
      throw error;
    }
  }

  /**
   * Get current driver context for trip logging
   * This provides driver information for trip logs and analytics
   */
  async getCurrentDriverContext(client, truckId) {
    try {
      const driverAssignment = await this.shiftValidator.getCurrentDriverAssignment(client, truckId);
      
      if (driverAssignment) {
        return {
          driver_id: driverAssignment.driver.id,
          driver_name: driverAssignment.driver.name,
          employee_id: driverAssignment.driver.employee_id,
          shift_id: driverAssignment.shift?.id || null,
          shift_type: driverAssignment.shift?.type || null,
          assignment_id: driverAssignment.assignment.id
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting current driver context:', error);
      return null;
    }
  }

  /**
   * Handle shift transitions during active trips
   * This is called when a shift change occurs during an active trip
   */
  async handleActiveShiftTransition(client, truckId, activeTripId) {
    try {
      // Get current and next shifts
      const currentTime = new Date();
      const shiftsQuery = `
        SELECT 
          ds.id,
          ds.driver_id,
          ds.shift_type,
          ds.start_time,
          ds.end_time,
          ds.status,
          d.full_name as driver_name
        FROM driver_shifts ds
        JOIN drivers d ON ds.driver_id = d.id
        WHERE ds.truck_id = $1
          AND ds.shift_date = CURRENT_DATE
          AND ds.status IN ('active', 'scheduled')
        ORDER BY ds.start_time
      `;

      const shiftsResult = await client.query(shiftsQuery, [truckId]);
      const shifts = shiftsResult.rows;

      const activeShift = shifts.find(s => s.status === 'active');
      const nextShift = shifts.find(s => s.status === 'scheduled' && s.start_time <= currentTime.toTimeString().slice(0, 8));

      if (activeShift && nextShift) {
        // Perform handover
        const handoverId = await this.shiftValidator.handleShiftHandover(
          client,
          truckId,
          activeShift.id,
          nextShift.id,
          activeTripId
        );

        return {
          handover_completed: true,
          handover_id: handoverId,
          outgoing_driver: activeShift.driver_name,
          incoming_driver: nextShift.driver_name,
          message: `Shift handover completed: ${activeShift.driver_name} → ${nextShift.driver_name}`
        };
      }

      return { handover_completed: false, message: 'No shift transition required' };

    } catch (error) {
      console.error('Error handling active shift transition:', error);
      throw error;
    }
  }
}

module.exports = ScannerShiftIntegration;
