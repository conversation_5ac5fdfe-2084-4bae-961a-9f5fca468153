-- Migration 024: Clean Problematic Trips and Assignments
-- Purpose: Clean up the problematic trips and assignments that were created with the old buggy logic
-- Date: 2025-07-09

-- Delete the problematic Trip 250 that has assignment switching
DELETE FROM trip_logs WHERE id = 250;

-- Clean up unused assignments that were created but not properly used
DELETE FROM assignments 
WHERE id IN (
  SELECT a.id 
  FROM assignments a
  LEFT JOIN trip_logs tl ON a.id = tl.assignment_id
  WHERE tl.id IS NULL
    AND a.created_at > '2025-07-09 02:00:00'
    AND a.assignment_code LIKE 'DYN-%'
);

-- Reset the truck to a clean state for testing
-- Keep only the base assignment and completed trips
UPDATE assignments 
SET status = 'assigned'
WHERE truck_id = 1 
  AND assignment_code = 'ASG-1751999482194-ELXLFX';

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 024 completed successfully: Cleaned problematic trips and assignments';
    RAISE NOTICE '- Deleted Trip 250 with assignment switching';
    RAISE NOTICE '- Cleaned up unused dynamic assignments';
    RAISE NOTICE '- Reset truck DT-100 to clean state for testing';
END $$;
