import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { assignmentsAPI } from '../../services/api';
import AssignmentsTable from './components/AssignmentsTable';
import AssignmentFormModal from './components/AssignmentFormModal';
import DeleteConfirmModal from '../../components/common/DeleteConfirmModal';
import toast from 'react-hot-toast';

const AssignmentsManagement = () => {
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
    hasNextPage: false,
    hasPrevPage: false
  });

  // Modal states
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedAssignment, setSelectedAssignment] = useState(null);

  // Filter and search states
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    truck_id: '',
    driver_id: '',
    date_from: '',
    date_to: '',
    sortBy: 'assigned_date',
    sortOrder: 'desc'
  });

  // Memoize filters to prevent infinite re-renders
  const memoizedFilters = useMemo(() => filters, [
    filters,
    filters.search,
    filters.status,
    filters.truck_id,
    filters.driver_id,
    filters.date_from,
    filters.date_to,
    filters.sortBy,
    filters.sortOrder
  ]);

  // Load assignments with error handling
  const loadAssignments = useCallback(async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: pagination.itemsPerPage,
        ...memoizedFilters
      };

      const response = await assignmentsAPI.getAll({ params });
      setAssignments(response.data.data);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Error loading assignments:', error);

      // Better error handling
      if (error.code === 'ERR_NETWORK') {
        toast.error('Network error. Please check your connection.');
      } else if (error.response?.status === 429) {
        toast.error('Too many requests. Please wait a moment.');
      } else {
        toast.error(error.response?.data?.message || 'Failed to load assignments');
      }
    } finally {
      setLoading(false);
    }
  }, [pagination.itemsPerPage, memoizedFilters]);

  // Remove debounced function - using proven pattern

  // Initial load
  useEffect(() => {
    loadAssignments();
  }, [loadAssignments]);

  // Handle page change
  const handlePageChange = (page) => {
    loadAssignments(page);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Handle sorting
  const handleSort = (column) => {
    const newSortOrder = filters.sortBy === column && filters.sortOrder === 'asc' ? 'desc' : 'asc';
    handleFilterChange({
      sortBy: column,
      sortOrder: newSortOrder
    });
  };

  // Handle search - use proven pattern
  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      search: '',
      status: '',
      truck_id: '',
      driver_id: '',
      date_from: '',
      date_to: '',
      sortBy: 'assigned_date',
      sortOrder: 'desc'
    });
  };

  // Handle create assignment
  const handleCreateAssignment = () => {
    setSelectedAssignment(null);
    setShowAssignmentModal(true);
  };

  // Handle edit assignment
  const handleEditAssignment = (assignment) => {
    setSelectedAssignment(assignment);
    setShowAssignmentModal(true);
  };

  // Handle delete assignment
  const handleDeleteAssignment = (assignment) => {
    setSelectedAssignment(assignment);
    setShowDeleteModal(true);
  };

  // Handle form submission
  const handleAssignmentSubmit = async (formData) => {
    try {
      if (selectedAssignment) {
        // Update existing assignment
        await assignmentsAPI.update(selectedAssignment.id, formData);
        toast.success('Assignment updated successfully');
      } else {
        // Create new assignment
        await assignmentsAPI.create(formData);
        toast.success('Assignment created successfully');
      }
      
      setShowAssignmentModal(false);
      loadAssignments(pagination.currentPage);
    } catch (error) {
      console.error('Error saving assignment:', error);
      const errorMessage = error.response?.data?.message || 'Failed to save assignment';
      toast.error(errorMessage);
      throw error; // Re-throw to prevent modal from closing
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      await assignmentsAPI.delete(selectedAssignment.id);
      toast.success('Assignment deleted successfully');
      setShowDeleteModal(false);
      
      // Reload current page or go to previous page if current page becomes empty
      const newTotalItems = pagination.totalItems - 1;
      const newTotalPages = Math.ceil(newTotalItems / pagination.itemsPerPage);
      const targetPage = pagination.currentPage > newTotalPages ? newTotalPages : pagination.currentPage;
      
      loadAssignments(Math.max(1, targetPage));
    } catch (error) {
      console.error('Error deleting assignment:', error);
      const errorMessage = error.response?.data?.message || 'Failed to delete assignment';
      toast.error(errorMessage);
    }
  };

  // Count active filters
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => 
    key !== 'sortBy' && key !== 'sortOrder' && value !== ''
  ).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Assignments Management</h1>
          <p className="text-secondary-600 mt-1">Manage truck and driver route assignments</p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={handleCreateAssignment}
            className="btn btn-primary"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Create Assignment
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {/* Search */}
          <div className="lg:col-span-2">
            <label htmlFor="search" className="block text-sm font-medium text-secondary-700 mb-1">
              Search
            </label>
            <input
              type="text"
              id="search"
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="Search assignments..."
              className="input"
            />
          </div>

          {/* Status Filter */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-secondary-700 mb-1">
              Status
            </label>
            <select
              id="status"
              value={filters.status}
              onChange={(e) => handleFilterChange({ status: e.target.value })}
              className="input"
            >
              <option value="">All Status</option>
              <option value="assigned">Assigned</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          {/* Date From */}
          <div>
            <label htmlFor="date_from" className="block text-sm font-medium text-secondary-700 mb-1">
              From Date
            </label>
            <input
              type="date"
              id="date_from"
              value={filters.date_from}
              onChange={(e) => handleFilterChange({ date_from: e.target.value })}
              className="input"
            />
          </div>

          {/* Date To */}
          <div>
            <label htmlFor="date_to" className="block text-sm font-medium text-secondary-700 mb-1">
              To Date
            </label>
            <input
              type="date"
              id="date_to"
              value={filters.date_to}
              onChange={(e) => handleFilterChange({ date_to: e.target.value })}
              className="input"
            />
          </div>

          {/* Clear Filters */}
          <div className="flex items-end">
            <button
              onClick={clearFilters}
              disabled={activeFiltersCount === 0}
              className="btn btn-secondary w-full disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear Filters
              {activeFiltersCount > 0 && (
                <span className="ml-1 bg-primary-100 text-primary-800 text-xs rounded-full px-2 py-0.5">
                  {activeFiltersCount}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Assignments Table */}
      <AssignmentsTable
        assignments={assignments}
        loading={loading}
        pagination={pagination}
        filters={filters}
        onPageChange={handlePageChange}
        onSort={handleSort}
        onEdit={handleEditAssignment}
        onDelete={handleDeleteAssignment}
      />

      {/* Assignment Form Modal */}
      {showAssignmentModal && (
        <AssignmentFormModal
          assignment={selectedAssignment}
          onClose={() => setShowAssignmentModal(false)}
          onSubmit={handleAssignmentSubmit}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedAssignment && (
        <DeleteConfirmModal
          isOpen={showDeleteModal}
          onClose={() => {
            setShowDeleteModal(false);
            setSelectedAssignment(null);
          }}
          onCancel={() => {
            setShowDeleteModal(false);
            setSelectedAssignment(null);
          }}
          onConfirm={handleDeleteConfirm}
          title="Delete Assignment"
          message={`Are you sure you want to delete the assignment for ${selectedAssignment.truck_number} with ${selectedAssignment.driver_name}? This action cannot be undone.`}
          confirmText="Delete Assignment"
        />
      )}
    </div>
  );
};

export default AssignmentsManagement;