-- Migration 023: Fix Stuck Trip 246
-- Purpose: Complete Trip 246 that is stuck in unloading_end status
-- Date: 2025-07-09

-- Update Trip 246 to completed status
UPDATE trip_logs 
SET 
  status = 'trip_completed',
  trip_completed_time = unloading_end_time + interval '48 seconds',
  total_duration_minutes = COALESCE(total_duration_minutes, 5),
  updated_at = CURRENT_TIMESTAMP,
  notes = COALESCE(notes, '{}')::jsonb || '{"manual_completion": true, "reason": "Fixed stuck trip in unloading_end status", "fixed_at": "2025-07-09T02:50:00.000Z"}'::jsonb
WHERE id = 246 AND status = 'unloading_end';

-- Delete incorrectly created Trip 247 (created due to the bug)
DELETE FROM trip_logs WHERE id = 247;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 023 completed successfully: Fixed stuck Trip 246';
    RAISE NOTICE '- Trip 246 status changed from unloading_end to trip_completed';
    RAISE NOTICE '- Trip 247 deleted (incorrectly created due to bug)';
    RAISE NOTICE '- 4-phase workflow integrity restored';
END $$;
