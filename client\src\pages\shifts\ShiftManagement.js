import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import shiftService from '../../services/shiftService';
import toast from 'react-hot-toast';

const ShiftManagement = () => {
  const { user } = useAuth();
  const [shifts, setShifts] = useState([]);
  const [trucks, setTrucks] = useState([]);
  const [drivers, setDrivers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [filters, setFilters] = useState({
    truck_id: '',
    driver_id: '',
    status: '',
    shift_type: ''
  });

  // Load initial data
  useEffect(() => {
    loadShifts();
    loadTrucks();
    loadDrivers();
  }, [selectedDate, filters]);

  const loadShifts = async () => {
    try {
      setLoading(true);
      const response = await shiftService.getShifts({
        shift_date: selectedDate,
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      });
      setShifts(response.data || []);
    } catch (error) {
      console.error('Error loading shifts:', error);
      toast.error('Failed to load shifts');
    } finally {
      setLoading(false);
    }
  };

  const loadTrucks = async () => {
    try {
      const response = await fetch('/api/trucks', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setTrucks(data.data || []);
      }
    } catch (error) {
      console.error('Error loading trucks:', error);
    }
  };

  const loadDrivers = async () => {
    try {
      const response = await fetch('/api/drivers', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('hauling_token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setDrivers(data.data || []);
      }
    } catch (error) {
      console.error('Error loading drivers:', error);
    }
  };

  const handleActivateShift = async (shiftId) => {
    try {
      await shiftService.activateShift(shiftId);
      toast.success('Shift activated successfully');
      loadShifts();
    } catch (error) {
      console.error('Error activating shift:', error);
      toast.error('Failed to activate shift');
    }
  };

  const handleCancelShift = async (shiftId) => {
    if (window.confirm('Are you sure you want to cancel this shift?')) {
      try {
        await shiftService.cancelShift(shiftId);
        toast.success('Shift cancelled successfully');
        loadShifts();
      } catch (error) {
        console.error('Error cancelling shift:', error);
        toast.error('Failed to cancel shift');
      }
    }
  };

  const getStatusColor = (status) => {
    return shiftService.getShiftStatusColor(status);
  };

  const getShiftTypeDisplay = (shiftType) => {
    return shiftService.getShiftTypeDisplay(shiftType);
  };

  const formatTime = (timeString) => {
    return shiftService.formatShiftTime(timeString);
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-secondary-900">
            🔄 Shift Management
          </h1>
          <p className="mt-2 text-secondary-600">
            Manage driver shifts and schedules for multi-driver truck operations
          </p>
        </div>

        {/* Controls */}
        <div className="bg-white rounded-lg shadow border border-secondary-200 p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4 items-end">
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Date
              </label>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Truck
              </label>
              <select
                value={filters.truck_id}
                onChange={(e) => setFilters(prev => ({ ...prev, truck_id: e.target.value }))}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md"
              >
                <option value="">All Trucks</option>
                {trucks.map(truck => (
                  <option key={truck.id} value={truck.id}>
                    {truck.truck_number}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Driver
              </label>
              <select
                value={filters.driver_id}
                onChange={(e) => setFilters(prev => ({ ...prev, driver_id: e.target.value }))}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md"
              >
                <option value="">All Drivers</option>
                {drivers.map(driver => (
                  <option key={driver.id} value={driver.id}>
                    {driver.full_name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md"
              >
                <option value="">All Statuses</option>
                <option value="scheduled">Scheduled</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary-700 mb-1">
                Shift Type
              </label>
              <select
                value={filters.shift_type}
                onChange={(e) => setFilters(prev => ({ ...prev, shift_type: e.target.value }))}
                className="w-full px-3 py-2 border border-secondary-300 rounded-md"
              >
                <option value="">All Types</option>
                <option value="day">Day Shift</option>
                <option value="night">Night Shift</option>
                <option value="custom">Custom Shift</option>
              </select>
            </div>

            <div>
              <button
                onClick={() => setShowCreateModal(true)}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                Create Shift
              </button>
            </div>
          </div>
        </div>

        {/* Shifts Table */}
        <div className="bg-white rounded-lg shadow border border-secondary-200 overflow-hidden">
          <div className="px-6 py-4 bg-secondary-50 border-b border-secondary-200">
            <h3 className="text-lg font-medium text-secondary-900">
              Shifts for {new Date(selectedDate).toLocaleDateString()}
            </h3>
          </div>

          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-secondary-500">Loading shifts...</p>
            </div>
          ) : shifts.length === 0 ? (
            <div className="p-8 text-center">
              <span className="text-4xl block mb-2">📅</span>
              <p className="text-secondary-500">No shifts found for the selected criteria</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-secondary-200">
                <thead className="bg-secondary-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Truck & Driver
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Shift Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Assignment
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-secondary-200">
                  {shifts.map((shift) => (
                    <tr key={shift.id} className="hover:bg-secondary-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-secondary-900">
                            {shift.truck_number}
                          </div>
                          <div className="text-sm text-secondary-500">
                            {shift.driver_name} • {shift.employee_id}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-secondary-900">
                            {getShiftTypeDisplay(shift.shift_type)}
                          </div>
                          <div className="text-sm text-secondary-500">
                            {new Date(shift.shift_date).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {formatTime(shift.start_time)} - {formatTime(shift.end_time)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(shift.status)}`}>
                          {shiftService.getShiftStatusDisplay(shift.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-900">
                          {shift.assignment_code || 'No Assignment'}
                        </div>
                        {shift.assignment_status && (
                          <div className="text-xs text-secondary-500">
                            Status: {shift.assignment_status}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          {shift.status === 'scheduled' && (
                            <button
                              onClick={() => handleActivateShift(shift.id)}
                              className="text-green-600 hover:text-green-900"
                            >
                              Activate
                            </button>
                          )}
                          {shift.status !== 'completed' && shift.status !== 'cancelled' && (
                            <button
                              onClick={() => handleCancelShift(shift.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Cancel
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create Shift Modal would go here */}
      {showCreateModal && (
        <CreateShiftModal
          trucks={trucks}
          drivers={drivers}
          selectedDate={selectedDate}
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false);
            loadShifts();
          }}
        />
      )}
    </div>
  );
};

// Placeholder for Create Shift Modal component
const CreateShiftModal = ({ trucks, drivers, selectedDate, onClose, onSuccess }) => {
  // This would be a full modal component for creating shifts
  // For now, just a placeholder
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">
          Create New Shift
        </h3>
        <p className="text-secondary-500 mb-4">
          Shift creation modal would be implemented here with form fields for truck, driver, shift type, and time.
        </p>
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-secondary-700 border border-secondary-300 rounded-md hover:bg-secondary-50"
          >
            Cancel
          </button>
          <button
            onClick={onSuccess}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Create Shift
          </button>
        </div>
      </div>
    </div>
  );
};

export default ShiftManagement;
