/**
 * Test File for Infinite Loop Detection
 * Tests both frontend and backend infinite loop scenarios
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class InfiniteLoopDetector {
  constructor() {
    this.frontendLogs = [];
    this.backendLogs = [];
    this.testResults = {
      frontend: { passed: false, errors: [] },
      backend: { passed: false, errors: [] }
    };
  }

  // Test 1: Frontend useCurrentDrivers Hook Stability
  async testFrontendHookStability() {
    console.log('🔍 Testing Frontend Hook Stability...');
    
    // Check for infinite loop patterns in useCurrentDrivers.js
    const hookPath = path.join(__dirname, 'client/src/hooks/useCurrentDrivers.js');
    const hookContent = fs.readFileSync(hookPath, 'utf8');
    
    // Test for proper dependency management
    const hasProperDependencies = this.checkDependencyManagement(hookContent);
    const hasStableCallbacks = this.checkCallbackStability(hookContent);
    const hasProperMemoization = this.checkMemoization(hookContent);
    
    if (hasProperDependencies && hasStableCallbacks && hasProperMemoization) {
      this.testResults.frontend.passed = true;
      console.log('✅ Frontend hook stability test PASSED');
    } else {
      this.testResults.frontend.errors.push('Hook dependency management issues detected');
      console.log('❌ Frontend hook stability test FAILED');
    }
  }

  // Test 2: Backend Query Optimization
  async testBackendQueryOptimization() {
    console.log('🔍 Testing Backend Query Optimization...');
    
    // Check ShiftDisplayHelper for proper caching and optimization
    const helperPath = path.join(__dirname, 'server/utils/ShiftDisplayHelper.js');
    const helperContent = fs.readFileSync(helperPath, 'utf8');
    
    const hasProperCaching = this.checkCaching(helperContent);
    const hasQueryOptimization = this.checkQueryOptimization(helperContent);
    
    if (hasProperCaching && hasQueryOptimization) {
      this.testResults.backend.passed = true;
      console.log('✅ Backend query optimization test PASSED');
    } else {
      this.testResults.backend.errors.push('Backend query optimization issues detected');
      console.log('❌ Backend query optimization test FAILED');
    }
  }

  // Test 3: Code Pattern Analysis for Infinite Loops
  async testCodePatternAnalysis() {
    console.log('🔍 Testing Code Pattern Analysis...');

    // Check TripsTable for proper memoization
    const tripsTablePath = path.join(__dirname, 'client/src/pages/trips/components/TripsTable.js');
    const tripsTableContent = fs.readFileSync(tripsTablePath, 'utf8');

    const hasProperTruckIdsMemo = tripsTableContent.includes('useMemo') &&
                                  tripsTableContent.includes('trips.map(trip => trip.truck_id)');

    // Check useCurrentDrivers for proper dependency management
    const hookPath = path.join(__dirname, 'client/src/hooks/useCurrentDrivers.js');
    const hookContent = fs.readFileSync(hookPath, 'utf8');

    const hasMemoizedTruckIds = hookContent.includes('memoizedTruckIds');
    const hasStableCallbacks = hookContent.includes('memoizedTruckIds, truckIdsParam');

    // Check backend for caching implementation
    const helperPath = path.join(__dirname, 'server/utils/ShiftDisplayHelper.js');
    const helperContent = fs.readFileSync(helperPath, 'utf8');

    const hasCaching = helperContent.includes('driverCache') && helperContent.includes('CACHE_TTL');
    const hasThrottling = helperContent.includes('REQUEST_THROTTLE') && helperContent.includes('THROTTLE_WINDOW');

    const allPatternsPassed = hasProperTruckIdsMemo && hasMemoizedTruckIds &&
                             hasStableCallbacks && hasCaching && hasThrottling;

    if (allPatternsPassed) {
      console.log('✅ Code pattern analysis test PASSED');
      console.log('  - TripsTable has proper memoization');
      console.log('  - useCurrentDrivers has stable dependencies');
      console.log('  - Backend has caching and throttling');
      return true;
    } else {
      console.log('❌ Code pattern analysis test FAILED');
      console.log(`  - TripsTable memoization: ${hasProperTruckIdsMemo ? '✅' : '❌'}`);
      console.log(`  - Hook memoized truck IDs: ${hasMemoizedTruckIds ? '✅' : '❌'}`);
      console.log(`  - Hook stable callbacks: ${hasStableCallbacks ? '✅' : '❌'}`);
      console.log(`  - Backend caching: ${hasCaching ? '✅' : '❌'}`);
      console.log(`  - Backend throttling: ${hasThrottling ? '✅' : '❌'}`);
      return false;
    }
  }

  // Helper methods for code analysis
  checkDependencyManagement(content) {
    // Check for proper useMemo/useCallback usage with stable dependencies
    const hasUseMemo = content.includes('useMemo');
    const hasStableDependencies = !content.includes('truckIds]') || content.includes('useMemo');
    return hasUseMemo && hasStableDependencies;
  }

  checkCallbackStability(content) {
    // Check that callbacks have stable dependencies
    const callbackPattern = /useCallback\([^,]+,\s*\[([^\]]*)\]/g;
    const matches = content.match(callbackPattern);
    
    if (!matches) return false;
    
    // Ensure no unstable dependencies like arrays created in render
    return !matches.some(match => match.includes('truckIds') && !match.includes('memoized'));
  }

  checkMemoization(content) {
    // Check for proper memoization of expensive operations
    return content.includes('useMemo') && content.includes('truckIds');
  }

  checkCaching(content) {
    // Check for caching mechanisms in backend
    return content.includes('cache') || content.includes('memoize') || content.includes('throttle');
  }

  checkQueryOptimization(content) {
    // Check for query optimization patterns
    const hasProperErrorHandling = content.includes('try') && content.includes('catch');
    const hasQueryLimits = content.includes('LIMIT') || content.includes('limit');
    return hasProperErrorHandling && hasQueryLimits;
  }

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting Infinite Loop Detection Tests...\n');

    await this.testFrontendHookStability();
    await this.testBackendQueryOptimization();

    const codePatternTestPassed = await this.testCodePatternAnalysis();

    console.log('\n📊 Test Results Summary:');
    console.log(`Frontend Hook Stability: ${this.testResults.frontend.passed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Backend Query Optimization: ${this.testResults.backend.passed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Code Pattern Analysis: ${codePatternTestPassed ? '✅ PASSED' : '❌ FAILED'}`);

    const allTestsPassed = this.testResults.frontend.passed &&
                          this.testResults.backend.passed &&
                          codePatternTestPassed;

    console.log(`\n🎯 Overall Result: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ TESTS FAILED'}`);

    if (!allTestsPassed) {
      console.log('\n🔧 Issues to fix:');
      this.testResults.frontend.errors.forEach(error => console.log(`  - Frontend: ${error}`));
      this.testResults.backend.errors.forEach(error => console.log(`  - Backend: ${error}`));
    }

    return allTestsPassed;
  }
}

// Export for use in other test files
module.exports = InfiniteLoopDetector;

// Run tests if called directly
if (require.main === module) {
  const detector = new InfiniteLoopDetector();
  detector.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}
